require "uri"

class Webhooks::InstagramProcessor
  def self.process(webhook)
    dataset = fetch_dataset(webhook.payload["resource"]["defaultDatasetId"])
    process_items(dataset)
  end

  private

  def self.fetch_dataset(dataset_id)
    client = Apify::Client.new("**********************************************")
    client.get_dataset_items(dataset_id)
  end

  def self.find_website_by_instagram_url(instagram_url)
    # Normalize the URL to handle different formats
    normalized_url = normalize_instagram_url(instagram_url)

    Service.find_by("options ->> 'instagram_url' = ?", normalized_url)
  end

  def self.normalize_instagram_url(url)
    # Remove trailing slash and ensure consistent format
    url.to_s.chomp('/')
  end

  def self.process_items(items)
    # Get Gallery media type for Instagram posts
    gallery_media_type = MediaType.find_by(name: "Gallery")

    items.each do |item|
      website = find_website_by_instagram_url(item["inputUrl"])
      binding.irb
      next unless website

      # Find or create Instagram MediaGroup for this website
      instagram_group = GalleryMediaGroup.find_or_create_for_website(website)

      # Create or update Media entity
      media_item = instagram_group.media.find_or_create_by(unique_id: item["id"]) do |media|
        update_media_item(media, item, gallery_media_type)
      end

      # Update existing media if needed
      unless media_item.new_record?
        update_media_item(media_item, item, gallery_media_type)
        media_item.save!
      end

      # Attach image
      attach_image(media_item, item)
    end
  end

  def self.update_media_item(media_item, item, media_type = nil)
    media_item.title = item["caption"]&.truncate(100) || "Instagram Post"
    media_item.caption = item["caption"]
    media_item.data = {
      media_type: item["type"],
      media_url: item["displayUrl"],
      url: item["url"],
      caption: item["caption"]
    }
    media_item.published_at = item["timestamp"]
    media_item.origin = "Instagram"
    media_item.media_type = media_type if media_type
    # website is set through media_group relationship
  end

  def self.attach_image(media_item, item)
    return if media_item.image.attached?

    begin
      media_item.image.attach(
        io: URI.open(item["displayUrl"]),
        filename: "#{item['id']}.jpg"
      )
    rescue => e
      Rails.logger.error "Failed to attach Instagram image for post #{item['id']}: #{e.message}"
    end
  end
end
