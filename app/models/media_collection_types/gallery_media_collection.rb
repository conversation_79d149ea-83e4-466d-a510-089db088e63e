# == Schema Information
#
# Table name: media_collections
#
#  id         :bigint           not null, primary key
#  name       :string
#  source     :string
#  type       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  website_id :bigint           not null
#
# Indexes
#
#  index_media_collections_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
class GalleryMediaCollection < MediaCollection

end
