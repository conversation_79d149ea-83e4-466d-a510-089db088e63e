# == Schema Information
#
# Table name: media_collections
#
#  id         :bigint           not null, primary key
#  name       :string
#  source     :string
#  type       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  website_id :bigint           not null
#
# Indexes
#
#  index_media_collections_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
class InstagramMediaCollection < MediaCollection

  def self.find_or_create_for_website(website)
    find_or_create_by(
      website: website,
      source: SOURCE_INSTAGRAM
    ) do |collection|
      collection.name = "Instagram Posts"
      collection.type = "InstagramMediaCollection"
    end
  end

  def add_instagram_post(post_data)
    media.find_or_create_by(unique_id: post_data[:id]) do |media_item|
      media_item.title = post_data[:caption]&.truncate(100) || "Instagram Post"
      media_item.caption = post_data[:caption]
      media_item.data = {
        media_type: post_data[:type],
        media_url: post_data[:displayUrl],
        url: post_data[:url],
        caption: post_data[:caption]
      }
      media_item.published_at = post_data[:timestamp]
      media_item.origin = "Instagram"
      media_item.website = website
    end
  end

end
