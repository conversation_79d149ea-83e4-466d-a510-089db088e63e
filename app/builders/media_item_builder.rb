class MediaItemBuilder
  def build(data)
    case data
    when Hash
      build_from_hash(data)
    else
      build_from_model(data)
    end
  end

  private

  def build_from_hash(data)
    MediaItemObject.new(
      SecureRandom.uuid,
      data[:title],
      data[:type],
      content: data[:content],
      data: data[:data],
      image: data[:image_url],
      icon: data[:icon_name],
      text: data[:text]
    )
  end

  def build_from_model(model)
    MediaItemObject.new(
      model.id,
      model.title,
      model.media_type.id,
      content: model.content,
      data: model.data&.transform_keys(&:to_sym),
      image: model.image,
      icon: model.icon&.name,
      text: model.text
    )
  end
end
