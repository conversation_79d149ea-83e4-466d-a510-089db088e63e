# frozen_string_literal: true

class Ui::ContentContainer < Phlex::HTML
  attr_reader :block_object, :id, :css

  def initialize(block_object, css: nil)
    @block_object = block_object
    @id = @block_object.id
    @css = css
  end

  def view_template
    div(
      id: content_container_dom_id,
      class: "#{parse_css} #{block_object.padding_class} #{block_object.padding_x_class} #{block_object.content_container_class} transition-all duration-300 ease-in-out outline-offset-2 mx-auto m-4 relative z-30 flex flex-col alignment-#{block_object.alignment} #{block_object.gap_y_class}"
    ) do
      yield
    end
  end

  private

  def content_container_dom_id
    "block-#{id}-content-container"
  end
end
