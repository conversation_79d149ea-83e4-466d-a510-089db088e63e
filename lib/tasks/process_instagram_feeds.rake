desc "Process websites Instagram feeds"
task process_instagram_feeds: :environment do
  services = Service.where(type: "Services::Instagram")

  new_websites, old_websites = services.partition do |service|
    # Check Instagram media count in new structure
    instagram_group = MediaGroup.find_by(website_id: service.website_id, source: MediaGroup::SOURCE_INSTAGRAM)
    instagram_group&.media&.count.to_i < 8
  end

  {
    new_websites: new_websites,
    old_websites: old_websites
  }.each do |type, run|
    next if run.empty?

    options = {
      resultsLimit: type == :new_websites ? 8 : 1
    }

    options[:onlyPostsNewerThan] = 1.day.ago.strftime("%Y-%m-%d") if type == :old_websites

    Apify::Client.new("**********************************************").fetch_instagram_posts(
      options:,
      params: run.map { |service| { website_id: service.website_id, url: service.instagram_url } }
    )
  end
end
