require "test_helper"

class InstagramMediaCollectionTest < ActiveSupport::TestCase
  def setup
    # Create test data without fixtures
    @account = Account.create!(name: "Test Account")
    @website = Website.create!(
      account: @account,
      name: "Test Website",
      domain: "test.com",
      email: "<EMAIL>",
      phone: "+************"
    )
  end

  test "should find or create Instagram MediaGroup for website" do
    assert_difference "MediaGroup.count", 1 do
      instagram_group = InstagramMediaGroup.find_or_create_for_website(@website)

      assert_not_nil instagram_group
      assert_equal "Instagram", instagram_group.source
      assert_equal "Instagram Posts", instagram_group.name
      assert_equal "InstagramMediaGroup", instagram_group.type
      assert_equal @website, instagram_group.website
    end
  end

  test "should not create duplicate Instagram MediaGroup for same website" do
    # Create first group
    first_group = InstagramMediaGroup.find_or_create_for_website(@website)

    # Try to create second group - should return the same one
    assert_no_difference "MediaGroup.count" do
      second_group = InstagramMediaGroup.find_or_create_for_website(@website)
      assert_equal first_group.id, second_group.id
    end
  end

  test "should add Instagram post to media group" do
    instagram_group = InstagramMediaGroup.find_or_create_for_website(@website)

    post_data = {
      id: "instagram_post_123",
      caption: "Test Instagram post",
      type: "IMAGE",
      displayUrl: "https://example.com/image.jpg",
      url: "https://instagram.com/p/test123",
      timestamp: "2023-01-01T12:00:00Z"
    }

    assert_difference "instagram_group.media.count", 1 do
      media_item = instagram_group.add_instagram_post(post_data)

      assert_not_nil media_item
      assert_equal "instagram_post_123", media_item.unique_id
      assert_equal "Test Instagram post", media_item.caption
      assert_equal "IMAGE", media_item.data["media_type"]
      assert_equal "https://example.com/image.jpg", media_item.data["media_url"]
      assert_equal "https://instagram.com/p/test123", media_item.data["url"]
      assert_equal "Instagram", media_item.origin
    end
  end

  test "should not create duplicate media for same Instagram post ID" do
    instagram_group = InstagramMediaGroup.find_or_create_for_website(@website)

    post_data = {
      id: "instagram_post_123",
      caption: "Test Instagram post",
      type: "IMAGE",
      displayUrl: "https://example.com/image.jpg",
      url: "https://instagram.com/p/test123",
      timestamp: "2023-01-01T12:00:00Z"
    }

    # Create first media item
    first_media = instagram_group.add_instagram_post(post_data)

    # Try to create second media item with same ID
    assert_no_difference "instagram_group.media.count" do
      second_media = instagram_group.add_instagram_post(post_data)
      assert_equal first_media.id, second_media.id
    end
  end

  test "should handle Instagram post with no caption" do
    instagram_group = InstagramMediaGroup.find_or_create_for_website(@website)

    post_data = {
      id: "instagram_post_no_caption",
      caption: nil,
      type: "IMAGE",
      displayUrl: "https://example.com/image.jpg",
      url: "https://instagram.com/p/test123",
      timestamp: "2023-01-01T12:00:00Z"
    }

    media_item = instagram_group.add_instagram_post(post_data)

    assert_equal "Instagram Post", media_item.title
    assert_nil media_item.caption
  end

  test "should truncate long captions for title" do
    instagram_group = InstagramMediaGroup.find_or_create_for_website(@website)

    long_caption = "A" * 150  # 150 characters

    post_data = {
      id: "instagram_post_long_caption",
      caption: long_caption,
      type: "IMAGE",
      displayUrl: "https://example.com/image.jpg",
      url: "https://instagram.com/p/test123",
      timestamp: "2023-01-01T12:00:00Z"
    }

    media_item = instagram_group.add_instagram_post(post_data)

    assert_equal 100, media_item.title.length
    assert_equal long_caption, media_item.caption
  end
end
