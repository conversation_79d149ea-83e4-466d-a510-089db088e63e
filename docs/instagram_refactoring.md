# Instagram Posts Refactoring

## Overview

This refactoring migrates Instagram post processing from the old `Instagram::Post` model to the new `Media` and `MediaCollection` architecture. The new implementation provides better integration with the dynamic media system and supports webhook-based processing.

## Changes Made

### 1. Updated Models

#### MediaCollection
- Added `SOURCE_INSTAGRAM` constant
- Added `instagram` scope
- Added `instagram?` method

#### Media
- Added Instagram-specific store_accessor fields: `:media_type`, `:media_url`, `:url`, `:caption`
- Added Instagram helper methods:
  - `instagram?` - checks if media belongs to Instagram collection
  - `instagram_post_id` - returns unique_id for Instagram posts
  - `instagram_caption` - returns caption for Instagram posts
  - `instagram_media_url` - returns media_url for Instagram posts
  - `instagram_url` - returns url for Instagram posts

#### InstagramMediaCollection
- New STI model extending MediaCollection
- `find_or_create_for_website(website)` - finds or creates Instagram collection for website
- `add_instagram_post(post_data)` - adds Instagram post to the collection

### 2. Updated Webhook Processing

#### Webhooks::InstagramProcessor
- Completely rewritten to use new Media/MediaCollection structure
- Creates `InstagramMediaCollection` for each website
- Creates `Media` entities with Instagram data stored in `data` JSON field
- Handles image attachment with error handling
- Prevents duplicate posts using `unique_id`

### 3. Updated Routes and Controllers

#### WebhooksController
- Uses `params[:event]` for event_type
- Instagram webhooks should be called as `POST /webhooks/instagram`

### 4. Migration Tools

#### Rake Tasks
- `migrate_instagram_posts` - migrates all existing Instagram::Post records
- `migrate_instagram_posts_for_website[website_id]` - migrates for specific website
- Updated `process_instagram_feeds` to check new structure

## Usage

### Webhook Endpoint
```
POST /webhooks/instagram
```

The webhook will:
1. Fetch data from Apify dataset
2. Create/find InstagramMediaCollection for each website
3. Create Media entities with Instagram data
4. Attach images from Instagram URLs

### Accessing Instagram Posts

```ruby
# Get Instagram MediaCollection for a website
instagram_collection = MediaCollection.instagram.find_by(website: website)

# Get all Instagram media for a website
instagram_media = website.media.joins(:media_collection).where(media_collections: { source: MediaCollection::SOURCE_INSTAGRAM })

# Check if media is from Instagram
media.instagram? # => true/false

# Get Instagram-specific data
media.instagram_caption
media.instagram_media_url
media.instagram_url
```

### Migration

To migrate existing Instagram::Post records:

```bash
# Migrate all posts to first website
rake migrate_instagram_posts

# Migrate posts to specific website
rake migrate_instagram_posts_for_website[123]
```

## Data Structure

### MediaCollection
```ruby
{
  name: "Instagram Posts",
  source: "Instagram",
  type: "InstagramMediaCollection",
  website_id: 123
}
```

### Media
```ruby
{
  unique_id: "instagram_post_id",
  title: "Truncated caption (100 chars)",
  origin: "Instagram",
  published_at: "2023-01-01T12:00:00Z",
  data: {
    caption: "Full Instagram caption",
    media_type: "IMAGE",
    media_url: "https://instagram.com/image.jpg",
    url: "https://instagram.com/p/post_id"
  }
}
```

## Testing

Run the tests:
```bash
rails test test/models/webhooks/instagram_processor_test.rb
rails test test/models/media_collection_types/instagram_media_collection_test.rb
```

## Backward Compatibility

The old `Instagram::Post` model is preserved for backward compatibility. The migration rake tasks can be used to transfer data to the new structure. Once migration is complete and verified, the old model can be deprecated.

## Benefits

1. **Unified Media System**: Instagram posts are now part of the same system as other media types
2. **Better Organization**: Posts are grouped by website in MediaGroups
3. **Webhook Processing**: Direct webhook processing without intermediate models
4. **Extensibility**: Easy to add new fields or functionality
5. **Performance**: Better querying and relationships through the Media system
